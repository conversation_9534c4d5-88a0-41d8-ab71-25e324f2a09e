// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
  ext {
    buildToolsVersion = "35.0.0"
    minSdkVersion = 24
    compileSdkVersion = 35
    targetSdkVersion = 34
    ndkVersion = "26.1.10909125"
    kotlinVersion = "1.9.24"
  }

  repositories {
    google()
    mavenCentral()
    maven { url("$rootDir/../node_modules/react-native/android") }
  }

  dependencies {
    classpath("com.android.tools.build:gradle:8.8.0")
    classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
  }
}

def reactNativeAndroidDir = new File(
  providers.exec {
    workingDir(rootDir)
    commandLine("node", "--print", "require.resolve('react-native/package.json')")
  }.standardOutput.asText.get().trim(),
  "../android"
)

allprojects {
  repositories {
    mavenCentral()
    google()
    maven { url("$rootDir/../node_modules/react-native/android") }
  }
}

// React Native plugin managed by settings.gradle autolinking






// Mapbox Maps Maven repository

allprojects {
  repositories {
    maven {
      url 'https://api.mapbox.com/downloads/v2/releases/maven'
      authentication { basic(BasicAuthentication) }
      credentials {
        username = 'mapbox'
        password = project.properties['MAPBOX_DOWNLOADS_TOKEN'] ?: ""
      }
    }
  }
}

// End Mapbox Maps Maven repository