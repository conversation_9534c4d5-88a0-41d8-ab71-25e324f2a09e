{"name": "fishivo", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "react-native start --port 8090", "start": "react-native start --port 8090", "android": "react-native run-android", "ios": "react-native run-ios", "android:emulator": "./scripts/start-emulator.sh", "android:build": "./scripts/android-build.sh", "android:full": "./scripts/start-emulator.sh && sleep 30 && ./scripts/android-build.sh", "android:run": "./scripts/run-android.sh", "android:quick": "./scripts/quick-run.sh", "clean:start": "./scripts/clean-start.sh", "clean": "cd android && ./gradlew clean && cd .. && rm -rf node_modules && npm install", "reset": "npx react-native-clean-project", "pods": "cd ios && pod install && cd ..", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace Fishivo.xcworkspace -scheme Fishivo -configuration Release -destination generic/platform=iOS -archivePath Fishivo.xcarchive archive", "lint": "eslint .", "test": "jest", "postinstall": "patch-package", "packages:install": "cd packages/backend && npm install && cd ../web && npm install", "packages:dev": "concurrently \"cd packages/backend && npm run dev\" \"cd packages/web && npm run dev\"", "packages:build": "cd packages/backend && npm run build && cd ../web && npm run build"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/geolocation": "^3.4.0", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/auth": "^22.2.1", "@react-native-google-signin/google-signin": "^14.0.1", "react-native-app-auth": "^7.2.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "@rnmapbox/maps": "^10.1.39", "aws-sdk": "^2.1692.0", "dotenv": "^16.5.0", "lucide-react-native": "^0.513.0", "react": "19.0.0", "react-native": "0.79.3", "react-native-config": "^1.5.5", "react-native-curved-bottom-bar": "^3.5.1", "react-native-device-info": "^14.0.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.26.0", "react-native-haptic-feedback": "^2.3.3", "react-native-image-picker": "^8.2.1", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.24.3", "react-native-permissions": "^4.1.5", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.12.0", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-view-shot": "^4.0.3", "react-native-wagmi-charts": "^2.7.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@react-native/babel-preset": "^0.79.3", "@react-native/metro-config": "^0.79.3", "@react-native/typescript-config": "^0.79.3", "@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "@types/react-native-vector-icons": "^6.4.18", "babel-plugin-module-resolver": "^5.0.2", "concurrently": "^8.2.2", "metro-react-native-babel-preset": "^0.77.0", "patch-package": "^8.0.0", "react-native-clean-project": "^4.0.3", "typescript": "~5.8.3", "wrangler": "^4.20.0"}, "private": true, "workspaces": ["packages/backend", "packages/web", "packages/shared"], "keywords": ["fishivo", "fishing", "social", "platform", "react-native", "mobile", "ios", "android"]}