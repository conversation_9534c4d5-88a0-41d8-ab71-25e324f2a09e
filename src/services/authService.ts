import { Linking } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './databaseService';
import Config from 'react-native-config';
import { authorize, AuthConfiguration } from 'react-native-app-auth';
import { API_CONFIG } from '../config';

// Environment configuration
console.log('🔍 AuthService Config Debug:');
console.log('Config.NEXT_PUBLIC_GOOGLE_CLIENT_ID:', Config.NEXT_PUBLIC_GOOGLE_CLIENT_ID);
console.log('Config.NEXT_PUBLIC_FACEBOOK_APP_ID:', Config.NEXT_PUBLIC_FACEBOOK_APP_ID);

const GOOGLE_CLIENT_ID = Config.NEXT_PUBLIC_GOOGLE_CLIENT_ID || process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
const FACEBOOK_APP_ID = Config.NEXT_PUBLIC_FACEBOOK_APP_ID || process.env.NEXT_PUBLIC_FACEBOOK_APP_ID;

// OAuth configurations
const googleConfig: AuthConfiguration = {
  issuer: 'https://accounts.google.com',
  clientId: GOOGLE_CLIENT_ID,
  redirectUrl: 'com.fishivo://oauth/google',
  scopes: ['openid', 'profile', 'email'],
  additionalParameters: {},
  customHeaders: {
    authorize: {},
    token: {},
    register: {}
  },
};

export interface UserProfile {
  id: string;
  email: string;
  name: string;
  picture?: string;
  provider: 'google' | 'facebook';
  username?: string;
  full_name?: string;
  avatar_url?: string;
  isPremium?: boolean;
}

export class AuthService {
  // Real Google OAuth authentication with react-native-app-auth
  static async signInWithGoogle(): Promise<UserProfile | null> {
    try {
      console.log('🔍 Starting Google OAuth...');
      
      // Use react-native-app-auth for proper OAuth flow
      const result = await authorize(googleConfig);
      
      console.log('✅ OAuth Success:', result);
      
      // Send token to backend for verification and user creation
      const response = await fetch(API_CONFIG.OAUTH.GOOGLE_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accessToken: result.accessToken,
          idToken: result.idToken,
        }),
      });

      if (!response.ok) {
        console.error('Backend verification failed:', response.status);
        return null;
      }

      const userData = await response.json();
      
      const userProfile: UserProfile = {
        id: userData.user.id,
        email: userData.user.email,
        name: userData.user.full_name || userData.user.name,
        picture: userData.user.avatar_url,
        provider: 'google',
        username: userData.user.username,
        full_name: userData.user.full_name,
        avatar_url: userData.user.avatar_url,
        isPremium: userData.user.is_premium || false,
      };

      await AsyncStorage.setItem('user', JSON.stringify(userProfile));
      await AsyncStorage.setItem('token', userData.token);
      
      return userProfile;
    } catch (error) {
      console.error('Google OAuth error:', error);
      return null;
    }
  }

  static async signInWithFacebook(): Promise<UserProfile | null> {
    try {
      console.log('🔍 Starting Facebook OAuth...');
      
      // Check if supabase is available
      if (!supabase) {
        console.error('❌ Supabase client not available');
        return null;
      }
      
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'facebook',
      });

      if (error) {
        console.error('Facebook OAuth error:', error);
        return null;
      }

      // Get user profile from database
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;

      // Check if user exists in our users table
      const { data: existingUser } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (!existingUser) {
        // Create new user record
        const newUser = {
          id: user.id,
          email: user.email || '',
          username: user.email?.split('@')[0] || 'user',
          full_name: user.user_metadata?.full_name || user.user_metadata?.name || 'User',
          avatar_url: user.user_metadata?.avatar_url || user.user_metadata?.picture,
          is_premium: false,
          total_catches: 0,
          total_spots: 0,
          reputation_score: 0,
        };

        const { data: createdUser, error: createError } = await supabase
          .from('users')
          .insert(newUser)
          .select()
          .single();

        if (createError) {
          console.error('Error creating user:', createError);
          return null;
        }

        const userProfile: UserProfile = {
          id: createdUser.id,
          email: createdUser.email,
          name: createdUser.full_name,
          picture: createdUser.avatar_url,
          provider: 'facebook',
          username: createdUser.username,
          full_name: createdUser.full_name,
          avatar_url: createdUser.avatar_url,
          isPremium: createdUser.is_premium,
        };

        await AsyncStorage.setItem('user', JSON.stringify(userProfile));
        return userProfile;
      }

      const userProfile: UserProfile = {
        id: existingUser.id,
        email: existingUser.email,
        name: existingUser.full_name,
        picture: existingUser.avatar_url,
        provider: 'facebook',
        username: existingUser.username,
        full_name: existingUser.full_name,
        avatar_url: existingUser.avatar_url,
        isPremium: existingUser.is_premium,
      };

      await AsyncStorage.setItem('user', JSON.stringify(userProfile));
      return userProfile;
    } catch (error) {
      console.error('Facebook OAuth error:', error);
      return null;
    }
  }

  // Sign out
  static async signOut(): Promise<void> {
    try {
      await supabase.auth.signOut();
      await AsyncStorage.removeItem('user');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  }

  // Get current user
  static async getCurrentUser(): Promise<UserProfile | null> {
    try {
      // Check if user is authenticated with Supabase
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        // Clean up local storage if no user is authenticated
        await AsyncStorage.removeItem('user');
        return null;
      }

      // Get user data from our users table
      const { data: userData } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (!userData) {
        // User exists in auth but not in our users table
        await AsyncStorage.removeItem('user');
        return null;
      }

      const userProfile: UserProfile = {
        id: userData.id,
        email: userData.email,
        name: userData.full_name,
        picture: userData.avatar_url,
        provider: 'google', // Default, can be updated based on metadata
        username: userData.username,
        full_name: userData.full_name,
        avatar_url: userData.avatar_url,
        isPremium: userData.is_premium,
      };

      await AsyncStorage.setItem('user', JSON.stringify(userProfile));
      return userProfile;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }
} 